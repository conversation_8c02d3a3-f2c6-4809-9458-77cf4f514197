<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
          <el-form-item label="推荐码" prop="code">
        <el-input v-model="dataForm.code" placeholder="推荐码"></el-input>
      </el-form-item>
          <el-form-item label="下载链接" prop="downloadUrl">
        <el-input v-model="dataForm.downloadUrl" placeholder="下载链接"></el-input>
      </el-form-item>
          <el-form-item label="版本所有者" prop="owner">
        <el-input v-model="dataForm.owner" placeholder="版本所有者"></el-input>
      </el-form-item>
          <el-form-item label="创建人" prop="createUser">
        <el-input v-model="dataForm.createUser" placeholder="创建人"></el-input>
      </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
        <el-input v-model="dataForm.createTime" placeholder="创建时间"></el-input>
      </el-form-item>
          <el-form-item label="更新人" prop="updateUser">
        <el-input v-model="dataForm.updateUser" placeholder="更新人"></el-input>
      </el-form-item>
          <el-form-item label="更新时间" prop="updateTime">
        <el-input v-model="dataForm.updateTime" placeholder="更新时间"></el-input>
      </el-form-item>
          <el-form-item label="是否删除" prop="isDelete">
        <el-input v-model="dataForm.isDelete" placeholder="是否删除"></el-input>
      </el-form-item>
      </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref } from "vue";
import baseService from "@/service/baseService";
import { ElMessage } from "element-plus";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '',  code: '',  downloadUrl: '',  owner: '',  createUser: '',  createTime: '',  updateUser: '',  updateTime: '',  isDelete: ''});

const rules = ref({
          code: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          downloadUrl: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          owner: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createUser: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          createTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          updateUser: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          updateTime: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ],
          isDelete: [
      { required: true, message: '必填项不能为空', trigger: 'blur' }
    ]
  });

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/voice/voicerecommend/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/voice/voicerecommend", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
