<template>
  <el-dialog v-model="visible" :title="!dataForm.id ? '新增' : '修改'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="rules" ref="dataFormRef" @keyup.enter="dataFormSubmitHandle()" label-width="120px">
      <el-form-item label="订单编号" prop="orderNumber">
        <el-input v-model="dataForm.orderNumber" placeholder="订单编号"></el-input>
      </el-form-item>
      <el-form-item label="订单标题" prop="orderTitle">
        <el-input v-model="dataForm.orderTitle" placeholder="订单标题"></el-input>
      </el-form-item>
      <el-form-item label="用户名" prop="voiceUser">
        <el-input v-model="dataForm.voiceUser" placeholder="用户名"></el-input>
      </el-form-item>
      <el-form-item label="金额" prop="amount">
        <el-input v-model="dataForm.amount" placeholder="金额"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="notes">
        <el-input v-model="dataForm.notes" placeholder="备注"></el-input>
      </el-form-item>
      <el-form-item label="支付方式" prop="paymentMethod">
        <el-input v-model="dataForm.paymentMethod" placeholder="支付方式"></el-input>
      </el-form-item>
      <el-form-item label="支付状态" prop="paymentStatus">
        <el-input v-model="dataForm.paymentStatus" placeholder="支付状态"></el-input>
      </el-form-item>
      <el-form-item label="结算批次" prop="settleId">
        <el-input v-model="dataForm.settleId" placeholder="结算批次"></el-input>
      </el-form-item>
      <el-form-item label="结算状态" prop="settleStatus">
        <el-input v-model="dataForm.settleStatus" placeholder="结算状态（未结算，结算中，已结算)"></el-input>
      </el-form-item>
      <el-form-item label="是否删除" prop="isDelete">
        <el-input v-model="dataForm.isDelete" placeholder="是否删除"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {reactive, ref} from "vue";
import baseService from "@/service/baseService";
import {ElMessage} from "element-plus";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: '', orderNumber: '', orderTitle: '', voiceUser: '', amount: '', notes: '', paymentMethod: '', paymentStatus: '', settleId: '', settleStatus: '', owner: '', createUser: '', createTime: '', updateUser: '', updateTime: '', isDelete: ''
});

const rules = ref({
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getInfo(id);
  }
};

// 获取信息
const getInfo = (id: number) => {
  baseService.get("/voice/voiceorder/" + id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

// 表单提交
const dataFormSubmitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    (!dataForm.id ? baseService.post : baseService.put)("/voice/voiceorder", dataForm).then((res) => {
      ElMessage.success({
        message: '成功',
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        }
      });
    });
  });
};

defineExpose({
  init
});
</script>
