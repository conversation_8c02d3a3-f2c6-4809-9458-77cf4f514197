/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    Cdk: typeof import('./src/components/Cdk.vue')['default']
    CloseButton: typeof import('./src/components/CloseButton.vue')['default']
    CommonVoiceItem: typeof import('./src/components/CommonVoiceItem.vue')['default']
    Contact: typeof import('./src/components/Contact.vue')['default']
    Download: typeof import('./src/components/Download.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTour: typeof import('element-plus/es')['ElTour']
    ElTourStep: typeof import('element-plus/es')['ElTourStep']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    Model: typeof import('./src/components/Model.vue')['default']
    Notice: typeof import('./src/components/Notice.vue')['default']
    Pay: typeof import('./src/components/Pay.vue')['default']
    RecommendVoice1: typeof import('./src/components/RecommendVoice1.vue')['default']
    RecommendVoice2: typeof import('./src/components/RecommendVoice2.vue')['default']
    RecommendVoice3: typeof import('./src/components/RecommendVoice3.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
    TutorialSimple: typeof import('./src/components/TutorialSimple.vue')['default']
    Update: typeof import('./src/components/Update.vue')['default']
    Version: typeof import('./src/components/Version.vue')['default']
    Vip: typeof import('./src/components/Vip.vue')['default']
  }
}
