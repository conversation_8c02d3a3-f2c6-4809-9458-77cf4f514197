<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云梦AI</title>
    <script src="https://cdn.tailwindcss.com/3.3.3"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('https://vip.123pan.cn/1815440059/18782309');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            color: white;
            min-height: 100vh;
        }
        
        .glassmorphism {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(10px);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #FF85A2 0%, #85BFFF 100%);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 133, 162, 0.3);
        }
        
        .feature-card {
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease forwards;
        }
        
        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="glassmorphism sticky top-0 z-50 py-4 px-6 shadow-lg">
        <div class="container mx-auto flex items-center justify-between">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-cube text-pink-500 text-xl"></i>
                </div>
                <span class="text-xl font-bold bg-gradient-to-r from-pink-400 to-blue-400 text-transparent bg-clip-text text-shadow">云梦AI</span>
            </div>
            
            <div class="hidden md:flex space-x-8">
                <a href="#features" class="text-white hover:text-pink-300 transition-colors">功能</a>
                <a href="#pricing" class="text-white hover:text-blue-300 transition-colors">定价</a>
                <a href="#about" class="text-white hover:text-pink-300 transition-colors">关于</a>
                <a href="#contact" class="text-white hover:text-blue-300 transition-colors">联系我们</a>
            </div>
            
            <button class="md:hidden text-white focus:outline-none" id="mobileMenuBtn">
                <i class="fas fa-bars text-xl"></i>
            </button>
        </div>
        
        <!-- 移动端菜单 -->
        <div class="md:hidden hidden glassmorphism mt-4 py-2 rounded-lg" id="mobileMenu">
            <a href="#features" class="block px-4 py-2 text-white hover:bg-pink-800/30 rounded transition-colors">功能</a>
            <a href="#pricing" class="block px-4 py-2 text-white hover:bg-blue-800/30 rounded transition-colors">定价</a>
            <a href="#about" class="block px-4 py-2 text-white hover:bg-pink-800/30 rounded transition-colors">关于</a>
            <a href="#contact" class="block px-4 py-2 text-white hover:bg-blue-800/30 rounded transition-colors">联系我们</a>
        </div>
    </nav>

    <!-- 主要内容区 -->
    <main class="container mx-auto px-6 py-16 md:py-24">
        <div class="flex flex-col lg:flex-row items-center justify-between gap-12">
            <!-- 左侧产品介绍 -->
            <div class="lg:w-1/2 fade-in" style="animation-delay: 0.1s;">
                <h1 class="text-4xl md:text-5xl font-bold mb-6 leading-tight text-shadow">重新定义你的声音</h1>
                <p class="text-lg md:text-xl text-blue-100 mb-8 leading-relaxed">
                    云梦AI 是一款实时变声工具，专为游戏玩家、内容创作者和语音聊天用户打造。我们的智能算法可以实时转换您的声音特征，让您在不同场景中展现多样化的声音魅力，为您的数字生活增添更多乐趣。
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <button class="btn-primary px-8 py-3 rounded-lg font-semibold text-lg">
                        免费下载 <i class="fas fa-arrow-down ml-2"></i>
                    </button>
                    <button class="glassmorphism px-8 py-3 rounded-lg font-semibold text-lg border border-blue-400 text-blue-100 hover:bg-blue-800/30 transition-colors" onclick="window.open('https://v.douyin.com/eOk9neTvrdE/')">
                        使用教程 <i class="fas fa-play-circle ml-2"></i>
                    </button>
                </div>
                
                <div class="mt-12 grid grid-cols-2 md:grid-cols-3 gap-4">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-pink-400 mr-2"></i>
                        <span class="text-sm">实时变声</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-blue-400 mr-2"></i>
                        <span class="text-sm">多种音色</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-pink-400 mr-2"></i>
                        <span class="text-sm">效果真实</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-blue-400 mr-2"></i>
                        <span class="text-sm">游戏整蛊</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-pink-400 mr-2"></i>
                        <span class="text-sm">语音聊天</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-blue-400 mr-2"></i>
                        <span class="text-sm">角色配音</span>
                    </div>
                </div>
            </div>
            
            <!-- 右侧产品截图 -->
            <div class="lg:w-1/2 fade-in" style="animation-delay: 0.3s;">
                <div class="glassmorphism p-2 rounded-xl shadow-2xl">
                    <img src="https://vip.123pan.cn/1815440059/18757675" alt="云梦AI界面截图" class="w-full h-auto rounded-lg">
                </div>
            </div>
        </div>
    </main>

    <!-- 功能部分 -->
    <section id="features" class="py-16 bg-blue-900/30 backdrop-blur-sm">
        <div class="container mx-auto px-6">
            <h2 class="text-3xl md:text-4xl font-bold text-center mb-16 text-shadow">强大功能</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="feature-card glassmorphism p-8 rounded-xl">
                    <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-blue-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                        <i class="fas fa-microphone text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-center mb-4">实时变声</h3>
                    <p class="text-blue-100 text-center">
                        采用先进的AI深度学习算法，实时处理音频信号，让您的声音转换为目标音色，效果自然逼真。
                    </p>
                </div>
                
                <div class="feature-card glassmorphism p-8 rounded-xl" style="animation-delay: 0.2s;">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-pink-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                        <i class="fas fa-users text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-center mb-4">效果自然</h3>
                    <p class="text-blue-100 text-center">
                        内置上百款音色，总有一款适合你。
                    </p>
                </div>
                
                <div class="feature-card glassmorphism p-8 rounded-xl" style="animation-delay: 0.4s;">
                    <div class="w-16 h-16 bg-gradient-to-r from-pink-500 to-blue-500 rounded-full flex items-center justify-center mb-6 mx-auto">
                        <i class="fas fa-wand-magic-sparkles text-white text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-center mb-4">先进算法</h3>
                    <p class="text-blue-100 text-center">
                        我们拥有更强大的咬字算法，相较于RVC咬字更加准确。
                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- 页脚 -->
    <footer class="bg-blue-900/50 py-12 backdrop-blur-sm">
        <div class="container mx-auto px-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-6 md:mb-0">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-cube text-pink-500 text-xl"></i>
                        </div>
                        <span class="text-xl font-bold bg-gradient-to-r from-pink-400 to-blue-400 text-transparent bg-clip-text text-shadow">云梦AI</span>
                    </div>
                </div>
                
                <div class="flex space-x-6">
                    <a href="https://v.douyin.com/yKUonOlHElc/" target="_blank" class="text-blue-200 hover:text-white transition-colors">
                        <i class="fa-brands fa-tiktok"></i>
                    </a>
                    <a href="https://space.bilibili.com/197691774" target="_blank" class="text-blue-200 hover:text-white transition-colors">
                        <i class="fa-brands fa-bilibili"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // 移动端菜单切换
        document.getElementById('mobileMenuBtn').addEventListener('click', function() {
            const menu = document.getElementById('mobileMenu');
            const icon = this.querySelector('i');

            if (menu.classList.contains('hidden')) {
                menu.classList.remove('hidden');
                icon.classList.remove('fa-bars');
                icon.classList.add('fa-times');
            } else {
                menu.classList.add('hidden');
                icon.classList.remove('fa-times');
                icon.classList.add('fa-bars');
            }
        });

        // 滚动动画
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in');
                }
            });
        }, { threshold: 0.1 });

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // 获取URL参数的函数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 调用推荐接口的函数
        async function callRecommendApi(rmby) {
            try {
                console.log('调用推荐接口，rmby参数:', rmby);

                // 配置API主机地址
                // let host = 'http://127.0.0.1:8080'; // 本地开发环境
                let host = 'http://www.yunmeng.space'; // 生产环境

                const apiUrl = `${host}/voice-changer/api/recommend/recommend?rmby=${encodeURIComponent(rmby)}`;
                console.log('请求URL:', apiUrl);

                const response = await fetch(apiUrl, {
                    method: 'GET',
                    mode: 'cors',
                    credentials: 'omit',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();
                console.log('API返回结果:', result);

                // 检查返回结果
                if (result.code === 0 && result.data && result.data.data) {
                    const apiData = result.data.data;

                    // 如果有downloadUrl，则更新下载链接
                    if (apiData.downloadUrl) {
                        updateDownloadLink(apiData.downloadUrl);
                        console.log('更新下载链接为:', apiData.downloadUrl);
                    }

                    return apiData;
                } else {
                    console.warn('API调用成功但数据格式异常:', result);
                    return null;
                }
            } catch (error) {
                console.error('调用推荐接口失败:', error);
                return null;
            }
        }



        // 更新下载链接的函数
        function updateDownloadLink(downloadUrl) {
            // 查找下载按钮
            const downloadBtn = document.querySelector('.btn-primary');
            if (downloadBtn) {
                // 移除原有的点击事件，添加新的下载链接
                downloadBtn.onclick = function() {
                    window.open(downloadUrl, '_blank');
                };
                console.log('下载按钮链接已更新');
            } else {
                console.warn('未找到下载按钮');
            }
        }

        // 页面加载完成后执行
        window.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，开始检查rmby参数');

            // 从URL参数中获取rmby值
            const rmby = getUrlParameter('rmby');

            if (rmby) {
                console.log('检测到rmby参数:', rmby);
                // 调用推荐接口
                callRecommendApi(rmby);
            } else {
                console.log('未检测到rmby参数');
            }
        });
    </script>
</body>
</html>