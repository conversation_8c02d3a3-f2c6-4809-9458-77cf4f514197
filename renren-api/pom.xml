<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>io.renren</groupId>
		<artifactId>renren-security</artifactId>
		<version>5.3.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>renren-api</artifactId>
	<packaging>jar</packaging>
	<description>renren-api</description>

	<dependencies>
		<dependency>
			<groupId>io.renren</groupId>
			<artifactId>renren-common</artifactId>
			<version>5.3.0</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>