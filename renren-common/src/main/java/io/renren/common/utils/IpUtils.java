/**
 * Copyright (c) 2018 人人开源 All rights reserved.
 *
 * https://www.renren.io
 *
 * 版权所有，侵权必究！
 */

package io.renren.common.utils;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;

/**
 * IP地址
 * 
 * @<NAME_EMAIL>
 */
public class IpUtils {
	private static Logger logger = LoggerFactory.getLogger(IpUtils.class);

	/**
	 * 获取IP地址
	 *
	 * 使用Nginx等反向代理软件， 则不能通过request.getRemoteAddr()获取IP地址
	 * 如果使用了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP地址，X-Forwarded-For中第一个非unknown的有效IP字符串，则为真实IP地址
	 */
	public static String getIpAddr(HttpServletRequest request) {
	    String unknown = "unknown";
    	String ip = null;
        try {
            ip = request.getHeader("x-forwarded-for");
            if (StrUtil.isEmpty(ip) || unknown.equalsIgnoreCase(ip)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (StrUtil.isEmpty(ip) || ip.length() == 0 || unknown.equalsIgnoreCase(ip)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (StrUtil.isEmpty(ip) || unknown.equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_CLIENT_IP");
            }
            if (StrUtil.isEmpty(ip) || unknown.equalsIgnoreCase(ip)) {
                ip = request.getHeader("HTTP_X_FORWARDED_FOR");
            }
            if (StrUtil.isEmpty(ip) || unknown.equalsIgnoreCase(ip)) {
                ip = request.getRemoteAddr();
            }
        } catch (Exception e) {
        	logger.error("IPUtils ERROR ", e);
        }

        return ip;
    }

    /**
     * 获取真实的单个IP地址
     *
     * 从可能包含多个IP的字符串中智能提取真实的公网IP地址
     * 优先返回IPv4公网IP，其次是内网IP，最后是其他类型IP
     *
     * @param request HTTP请求对象
     * @return 真实的单个IP地址
     */
    public static String getRealIpAddr(HttpServletRequest request) {
        // 先获取原始IP字符串
        String originalIp = getIpAddr(request);

        // 提取真实IP
        String realIp = extractRealIp(originalIp);

        logger.debug("原始IP: {}, 提取的真实IP: {}", originalIp, realIp);

        return realIp;
    }

    /**
     * 从IP字符串中提取真实的公网IP
     * 处理多个IP用逗号分隔的情况，优先返回IPv4公网IP
     *
     * @param ipString 可能包含多个IP的字符串
     * @return 真实的单个IP地址
     */
    private static String extractRealIp(String ipString) {
        if (StrUtil.isEmpty(ipString)) {
            return null;
        }

        // 移除空格并按逗号分割
        String[] ips = ipString.replaceAll("\\s", "").split(",");

        // 第一轮：寻找IPv4公网IP
        for (String ip : ips) {
            if (isValidPublicIPv4(ip)) {
                logger.debug("找到IPv4公网IP: {}", ip);
                return ip;
            }
        }

        // 第二轮：寻找任何有效的IPv4地址（包括内网）
        for (String ip : ips) {
            if (isValidIPv4(ip) && !isLocalhost(ip)) {
                logger.debug("找到IPv4地址: {}", ip);
                return ip;
            }
        }

        // 第三轮：返回第一个非unknown、非空的IP（可能是IPv6）
        for (String ip : ips) {
            if (StrUtil.isNotEmpty(ip) && !"unknown".equalsIgnoreCase(ip) && !isLocalhost(ip)) {
                logger.debug("找到其他类型IP: {}", ip);
                return ip;
            }
        }

        // 如果都没找到，返回第一个非空IP
        return ips.length > 0 ? ips[0] : ipString;
    }

    /**
     * 判断是否为有效的IPv4地址
     */
    private static boolean isValidIPv4(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为公网IPv4地址
     */
    private static boolean isValidPublicIPv4(String ip) {
        if (!isValidIPv4(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        int first = Integer.parseInt(parts[0]);
        int second = Integer.parseInt(parts[1]);

        // 排除内网IP段
        // 10.0.0.0 - **************
        if (first == 10) {
            return false;
        }

        // ********** - **************
        if (first == 172 && second >= 16 && second <= 31) {
            return false;
        }

        // *********** - ***************
        if (first == 192 && second == 168) {
            return false;
        }

        // 排除本地回环地址 ********* - ***************
        if (first == 127) {
            return false;
        }

        // 排除链路本地地址 *********** - ***************
        if (first == 169 && second == 254) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否为本地回环地址
     */
    private static boolean isLocalhost(String ip) {
        return "127.0.0.1".equals(ip) || "localhost".equals(ip) || "0.0.0.0".equals(ip) || "::1".equals(ip);
    }

    /**
     * 判断是否为有效的IPv4地址
     */
    private static boolean isValidIPv4(String ip) {
        if (StrUtil.isEmpty(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }

        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 判断是否为公网IPv4地址
     */
    private static boolean isValidPublicIPv4(String ip) {
        if (!isValidIPv4(ip)) {
            return false;
        }

        String[] parts = ip.split("\\.");
        int first = Integer.parseInt(parts[0]);
        int second = Integer.parseInt(parts[1]);

        // 排除内网IP段
        // 10.0.0.0 - **************
        if (first == 10) {
            return false;
        }

        // ********** - **************
        if (first == 172 && second >= 16 && second <= 31) {
            return false;
        }

        // *********** - ***************
        if (first == 192 && second == 168) {
            return false;
        }

        // 排除本地回环地址 ********* - ***************
        if (first == 127) {
            return false;
        }

        // 排除链路本地地址 *********** - ***************
        if (first == 169 && second == 254) {
            return false;
        }

        return true;
    }

    /**
     * 判断是否为本地回环地址
     */
    private static boolean isLocalhost(String ip) {
        return "127.0.0.1".equals(ip) || "localhost".equals(ip) || "0.0.0.0".equals(ip) || "::1".equals(ip);
    }
	
}
