package io.renren.modules.voice.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * 推荐码列表
 *
 * <AUTHOR> <PERSON>@gmail.com
 * @since 1.0.0 2025-07-29
 */
@Data
@ApiModel(value = "推荐码列表")
public class VoiceRecommendDTO implements Serializable {
    private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "ID")
	private Long id;

	@ApiModelProperty(value = "推荐码")
	private String code;

	@ApiModelProperty(value = "下载链接")
	private String downloadUrl;

	@ApiModelProperty(value = "版本所有者")
	private String owner;

	@ApiModelProperty(value = "创建人")
	private String createUser;

	@ApiModelProperty(value = "创建时间")
	private Date createTime;

	@ApiModelProperty(value = "更新人")
	private String updateUser;

	@ApiModelProperty(value = "更新时间")
	private Date updateTime;

	@ApiModelProperty(value = "是否删除")
	private Integer isDelete;


}