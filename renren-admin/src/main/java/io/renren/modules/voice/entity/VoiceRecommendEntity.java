package io.renren.modules.voice.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 推荐码列表
 *
 * <AUTHOR>
 * @since 1.0.0 2025-07-29
 */
@Data
@TableName("voice_recommend")
public class VoiceRecommendEntity {

    /**
     * ID
     */
	private Long id;
    /**
     * 推荐码
     */
	private String code;
    /**
     * 下载链接
     */
	private String downloadUrl;
    /**
     * 版本所有者
     */
	private String owner;
    /**
     * 创建人
     */
	private String createUser;
    /**
     * 创建时间
     */
	private Date createTime;
    /**
     * 更新人
     */
	private String updateUser;
    /**
     * 更新时间
     */
	private Date updateTime;
    /**
     * 是否删除
     */
	private Integer isDelete;
}