package io.renren.modules.voice.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.Date;

/**
 * 推荐码列表
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 * @since 1.0.0 2025-07-29
 */
@Data
public class VoiceRecommendExcel {
    @ExcelProperty(value = "ID")
    private Long id;
    @ExcelProperty(value = "推荐码")
    private String code;
    @ExcelProperty(value = "下载链接")
    private String downloadUrl;
    @ExcelProperty(value = "版本所有者")
    private String owner;
    @ExcelProperty(value = "创建人")
    private String createUser;
    @ExcelProperty(value = "创建时间")
    private Date createTime;
    @ExcelProperty(value = "更新人")
    private String updateUser;
    @ExcelProperty(value = "更新时间")
    private Date updateTime;
    @ExcelProperty(value = "是否删除")
    private Integer isDelete;

}