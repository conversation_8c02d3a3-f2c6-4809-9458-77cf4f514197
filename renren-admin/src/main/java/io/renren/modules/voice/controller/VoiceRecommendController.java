package io.renren.modules.voice.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.voice.dto.VoiceRecommendDTO;
import io.renren.modules.voice.excel.VoiceRecommendExcel;
import io.renren.modules.voice.service.VoiceRecommendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 推荐码列表
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2025-07-29
 */
@RestController
@RequestMapping("voice/voicerecommend")
@Api(tags="推荐码列表")
public class VoiceRecommendController {
    @Autowired
    private VoiceRecommendService voiceRecommendService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("voice:voicerecommend:page")
    public Result<PageData<VoiceRecommendDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<VoiceRecommendDTO> page = voiceRecommendService.page(params);

        return new Result<PageData<VoiceRecommendDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("voice:voicerecommend:info")
    public Result<VoiceRecommendDTO> get(@PathVariable("id") Long id){
        VoiceRecommendDTO data = voiceRecommendService.get(id);

        return new Result<VoiceRecommendDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("voice:voicerecommend:save")
    public Result save(@RequestBody VoiceRecommendDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        voiceRecommendService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("voice:voicerecommend:update")
    public Result update(@RequestBody VoiceRecommendDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        voiceRecommendService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("voice:voicerecommend:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        voiceRecommendService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("voice:voicerecommend:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<VoiceRecommendDTO> list = voiceRecommendService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "推荐码列表", list, VoiceRecommendExcel.class);
    }

}