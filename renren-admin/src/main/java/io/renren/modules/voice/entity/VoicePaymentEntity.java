package io.renren.modules.voice.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 付款方式
 *
 * <AUTHOR>
 * @since 1.0.0 2024-04-14
 */
@Data
@TableName("voice_payment")
public class VoicePaymentEntity {

    /**
     * ID
     */
	private Long id;
    /**
     * 名称
     */
	private String name;
    /**
     * 类型
     */
	private String type;
    /**
     * 发卡网支付ID
     */
	private Integer paymentId;
    /**
     * 参数
     */
	private String params;
    /**
     * 状态(启用、禁用)
     */
	private String status;
    /**
     * 顺序
     */
	private Integer paymentIndex;
    /**
     * 备注
     */
	private String note;
    /**
     * 版本所有者
     */
	private String owner;
    /**
     * 创建人
     */
	private String createUser;
    /**
     * 创建时间
     */
	private Date createTime;
    /**
     * 更新人
     */
	private String updateUser;
    /**
     * 更新时间
     */
	private Date updateTime;
    /**
     * 是否删除
     */
	private Integer isDelete;
}
