package io.renren.modules.voice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.renren.common.service.impl.CrudServiceImpl;
import io.renren.modules.voice.dao.VoiceRecommendDao;
import io.renren.modules.voice.dto.VoiceRecommendDTO;
import io.renren.modules.voice.entity.VoiceRecommendEntity;
import io.renren.modules.voice.service.VoiceRecommendService;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 推荐码列表
 *
 * <AUTHOR> sunlight<PERSON>@gmail.com
 * @since 1.0.0 2025-07-29
 */
@Service
public class VoiceRecommendServiceImpl extends CrudServiceImpl<VoiceRecommendDao, VoiceRecommendEntity, VoiceRecommendDTO> implements VoiceRecommendService {

    @Override
    public QueryWrapper<VoiceRecommendEntity> getWrapper(Map<String, Object> params){
        String id = (String)params.get("id");

        QueryWrapper<VoiceRecommendEntity> wrapper = new QueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(id), "id", id);

        return wrapper;
    }


}