package io.renren.modules.api.service;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import io.renren.common.exception.ApiException;
import io.renren.common.redis.RedisUtils;
import io.renren.common.utils.StaticConstant;
import io.renren.modules.api.dto.ApiUserDTO;
import io.renren.modules.api.mapper.ApiUserMapper;
import io.renren.modules.sys.service.SysParamsService;
import io.renren.modules.voice.dao.*;
import io.renren.modules.voice.entity.VoiceUserEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;

@Service
public class ApiUserService {
    Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private VoiceUserDao voiceUserDao;

    @Resource
    private ApiUserMapper apiUserMapper;

    @Resource
    private ApiInheritService apiInheritService;

    @Resource
    private SysParamsService sysParamsService;

    @Resource
    private ApiRecommendService apiRecommendService;

    @Transactional(rollbackFor = Exception.class)
    public String register(ApiUserDTO apiUserDTO) {
        List<VoiceUserEntity> userCheck = apiUserMapper.registerCheckUsername(apiUserDTO);
        if (!userCheck.isEmpty()) {
            throw new ApiException("存在同名账号");
        }

        List<VoiceUserEntity> addressCheck = apiUserMapper.registerCheckAddress(apiUserDTO);
        if (!addressCheck.isEmpty()) {
            boolean ableToRegister = true;
            long currentTime = DateTime.now().getTime();
            for (VoiceUserEntity voiceUserEntity : addressCheck) {
                long oldTime = voiceUserEntity.getCreateTime().getTime();
                if (currentTime - oldTime < 2592000000L) {
                    // 相同机器码30天内注册过，不允许注册
                    ableToRegister = false;
                }
            }

            if (!ableToRegister) {
                throw new ApiException("当前电脑已注册过，请尝试找回密码");
            }
        }

        VoiceUserEntity voiceUserEntity = new VoiceUserEntity();
        voiceUserEntity.setUsername(apiUserDTO.getUsername());
        voiceUserEntity.setPassword(apiUserDTO.getPassword());
        voiceUserEntity.setEmail(apiUserDTO.getEmail());
        voiceUserEntity.setPhone(apiUserDTO.getPhone());
        voiceUserEntity.setStatus(StaticConstant.StatusOnline);
        voiceUserEntity.setLastActiveTime(new Date());
        voiceUserEntity.setRegisterAddress(apiUserDTO.getCurrentAddress());
        voiceUserEntity.setCurrentAddress(apiUserDTO.getCurrentAddress());
        voiceUserEntity.setRecommendBy(apiRecommendService.getRecommend(apiUserDTO.getIpAddr()));
        voiceUserEntity.setNote(apiUserDTO.getNote() + '-' + apiUserDTO.getIpAddr());
        voiceUserEntity.setOwner(apiUserDTO.getOwner());
        voiceUserDao.insert(voiceUserEntity);

        Map<String, String> resultData = new HashMap<>();
        resultData.put("message", "注册成功，即将自动登录");

        // 音色继承(不再自动继承)
        // apiInheritService.inheritAuthority(voiceUserEntity, resultData, false);

        return resultData.get("message");
    }

    @Transactional(rollbackFor = Exception.class)
    public void login(ApiUserDTO apiUserDTO) {
        // 判断是否存在
        QueryWrapper<VoiceUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", apiUserDTO.getUsername());
        queryWrapper.eq("password", apiUserDTO.getPassword());
        queryWrapper.eq("owner", apiUserDTO.getOwner());
        queryWrapper.eq("is_delete", 0);
        VoiceUserEntity voiceUser = voiceUserDao.selectOne(queryWrapper);

        if (voiceUser == null) {
            throw new ApiException("账号或密码错误");
        }

        // 判断状态
        if (Objects.equals(voiceUser.getStatus(), StaticConstant.StatusDisable)) {
            throw new ApiException("账号已禁用");
        }

        // 判断是否在别处有登录
        if (!Objects.equals(apiUserDTO.getCurrentAddress(), voiceUser.getCurrentAddress())) {
            long timeOld = voiceUser.getLastActiveTime().getTime();
            long timeNow = new Date().getTime();

            if (!Objects.equals(sysParamsService.getValue(StaticConstant.SysParamLoginWithoutCheck), "1")) {
                // 账号在线，且与现在间隔小于5分钟
                if (Objects.equals(voiceUser.getStatus(), StaticConstant.StatusOnline) && timeNow - timeOld < 10 * 60 * 1000) {
                    throw new ApiException("账号已在别处登录，请先退出登录");
                }
            }
        }

        // 成功登录，更新信息
        VoiceUserEntity update = new VoiceUserEntity();
        update.setStatus(StaticConstant.StatusOnline);
        update.setLastActiveTime(new Date());
        update.setCurrentAddress(apiUserDTO.getCurrentAddress());

        UpdateWrapper<VoiceUserEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", voiceUser.getId());
        voiceUserDao.update(update, updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void logout(ApiUserDTO apiUserDTO) {
        // 判断是否存在
        QueryWrapper<VoiceUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", apiUserDTO.getUsername());
        queryWrapper.eq("owner", apiUserDTO.getOwner());
        queryWrapper.eq("is_delete", 0);
        VoiceUserEntity voiceUser = voiceUserDao.selectOne(queryWrapper);

        if (voiceUser == null) {
            throw new ApiException("无此账号:" + apiUserDTO.getUsername());
        }

        // 退出成功，更新信息
        VoiceUserEntity update = new VoiceUserEntity();
        update.setStatus(StaticConstant.StatusOffline);
        update.setLastActiveTime(new Date());
        update.setCurrentAddress(apiUserDTO.getCurrentAddress());

        UpdateWrapper<VoiceUserEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", voiceUser.getId());
        voiceUserDao.update(update, updateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void find(ApiUserDTO apiUserDTO) {
        // 判断是否存在
        QueryWrapper<VoiceUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("username", apiUserDTO.getUsername());
        queryWrapper.eq("phone", apiUserDTO.getPhone());
        queryWrapper.eq("owner", apiUserDTO.getOwner());
        queryWrapper.eq("is_delete", 0);
        VoiceUserEntity voiceUser = voiceUserDao.selectOne(queryWrapper);

        if (voiceUser == null) {
            throw new ApiException("账号或手机号错误");
        }

        // 判断状态
        if (Objects.equals(voiceUser.getStatus(), StaticConstant.StatusDisable)) {
            throw new ApiException("账号已禁用");
        }

        // 可以改密
        VoiceUserEntity update = new VoiceUserEntity();
        update.setStatus(StaticConstant.StatusOffline);
        update.setLastActiveTime(new Date());
        update.setCurrentAddress(apiUserDTO.getCurrentAddress());
        update.setPassword(apiUserDTO.getPassword());

        UpdateWrapper<VoiceUserEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", voiceUser.getId());
        voiceUserDao.update(update, updateWrapper);
    }

    public VoiceUserEntity getEntityByUsername(ApiUserDTO apiUserDTO) {
        QueryWrapper<VoiceUserEntity> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("username", apiUserDTO.getUsername());
        userQueryWrapper.eq("owner", apiUserDTO.getOwner());
        userQueryWrapper.eq("is_delete", 0);
        VoiceUserEntity voiceUserEntity = voiceUserDao.selectOne(userQueryWrapper);

        if (voiceUserEntity == null) {
            throw new ApiException("查询不到用户");
        }

        // 判断状态
        if (Objects.equals(voiceUserEntity.getStatus(), StaticConstant.StatusDisable)) {
            throw new ApiException("账号已禁用");
        }

        return voiceUserEntity;
    }

    public VoiceUserEntity getEntityByUsernameAndPassword(ApiUserDTO apiUserDTO) {
        QueryWrapper<VoiceUserEntity> userQueryWrapper = new QueryWrapper<>();
        userQueryWrapper.eq("username", apiUserDTO.getUsername());
        userQueryWrapper.eq("password", apiUserDTO.getPassword());
        userQueryWrapper.eq("owner", apiUserDTO.getOwner());
        userQueryWrapper.eq("is_delete", 0);
        VoiceUserEntity voiceUserEntity = voiceUserDao.selectOne(userQueryWrapper);

        if (voiceUserEntity == null) {
            throw new ApiException("账号密码错误");
        }

        // 判断状态
        if (Objects.equals(voiceUserEntity.getStatus(), StaticConstant.StatusDisable)) {
            throw new ApiException("账号已禁用");
        }

        return voiceUserEntity;
    }

    public ApiUserDTO getUserInfo(ApiUserDTO apiUserDTO) {
        VoiceUserEntity voiceUser = getEntityByUsernameAndPassword(apiUserDTO);

        ApiUserDTO res = new ApiUserDTO();
        res.setUsername(voiceUser.getUsername());
        res.setPassword(voiceUser.getPassword());
        res.setBalance(voiceUser.getBalance());
        res.setTotal(voiceUser.getTotal());
        res.setVipExpire(voiceUser.getVipExpire());
        res.setEmail(voiceUser.getEmail());
        res.setOwner(voiceUser.getOwner());

        return res;
    }

    @Transactional(rollbackFor = Exception.class)
    public void heartbeat(ApiUserDTO apiUserDTO) {
        String lockKey = apiUserDTO.getUsername() + apiUserDTO.getOwner();

        boolean locked = false;
        try {
            locked = redisUtils.blockLock(lockKey);
        } catch (Exception e) {
            logger.error("Redis异常，心跳直接成功", e);
            return;
        }

        if (locked) {
            try {
                VoiceUserEntity voiceUserEntity = getEntityByUsernameAndPassword(apiUserDTO);

                if (!Objects.equals(apiUserDTO.getCurrentAddress(), voiceUserEntity.getCurrentAddress())) {
                    throw new ApiException("当前账号已在别处登录");
                }

                // 更新在线时间
                voiceUserEntity.setLastActiveTime(DateTime.now());
                voiceUserEntity.setUpdateUser(voiceUserEntity.getUsername());
                voiceUserEntity.setStatus(StaticConstant.StatusOnline);
                voiceUserDao.updateById(voiceUserEntity);
            } finally {
                redisUtils.unlock(lockKey);
            }
        } else {
            throw new ApiException(StaticConstant.RedisTryLockError);
        }
    }
}
