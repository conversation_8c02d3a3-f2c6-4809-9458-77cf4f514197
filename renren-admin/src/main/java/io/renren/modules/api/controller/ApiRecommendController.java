package io.renren.modules.api.controller;

import io.renren.common.annotation.RequestVerify;
import io.renren.common.exception.ApiException;
import io.renren.common.utils.IpUtils;
import io.renren.common.utils.JsonUtils;
import io.renren.common.utils.Result;
import io.renren.common.utils.StaticConstant;
import io.renren.common.utils.VerifyUtils;
import io.renren.common.validator.AssertUtils;
import io.renren.modules.api.common.CommonData;
import io.renren.modules.api.common.VerifyResult;
import io.renren.modules.api.dto.OwnerDTO;
import io.renren.modules.api.service.ApiRecommendService;
import io.renren.modules.voice.dto.VoiceRecommendDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("api/recommend")
@Api(tags="推荐管理")
public class ApiRecommendController {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private ApiRecommendService apiRecommendService;

    @ApiOperation("推荐接口")
    @GetMapping("recommend")
    public String recommend(@RequestParam(value = "rmby", required = false) String rmby, HttpServletRequest request) {
        try {
            // 获取客户端真实IP地址（使用新的方法获取单个真实IP）
            String clientIp = IpUtils.getRealIpAddr(request);

            // 同时记录原始IP用于对比调试
            String originalIp = IpUtils.getIpAddr(request);

            logger.info("推荐接口调用 - 参数rmby: {}, 真实IP: {}, 原始IP: {}", rmby, clientIp, originalIp);

            VoiceRecommendDTO recommend = apiRecommendService.recommend(rmby, clientIp);

            Map<String, String> resultData = new HashMap<>();
            resultData.put("rmby", rmby);
            resultData.put("clientIp", clientIp);
            if (recommend != null) {
                resultData.put("code", recommend.getCode());
                resultData.put("downloadUrl", recommend.getDownloadUrl());
                resultData.put("owner", recommend.getOwner());
            }

            return JsonUtils.toJsonString(
                    new Result<>().ok(
                            new CommonData<>().ok("推荐接口调用成功").data(resultData)
                    )
            );
        } catch (Exception e) {
            String errorMessage = "推荐接口调用异常：" + e.getMessage();
            logger.error(errorMessage);
            return JsonUtils.toJsonString(
                    new Result<>().error(errorMessage)
            );
        }
    }
}
