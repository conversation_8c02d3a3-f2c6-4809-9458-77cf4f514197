package io.renren.modules.office.controller;

import io.renren.common.annotation.LogOperation;
import io.renren.common.constant.Constant;
import io.renren.common.page.PageData;
import io.renren.common.utils.ExcelUtils;
import io.renren.common.utils.Result;
import io.renren.common.validator.AssertUtils;
import io.renren.common.validator.ValidatorUtils;
import io.renren.common.validator.group.AddGroup;
import io.renren.common.validator.group.DefaultGroup;
import io.renren.common.validator.group.UpdateGroup;
import io.renren.modules.office.dto.OfficeRepairDTO;
import io.renren.modules.office.excel.OfficeRepairExcel;
import io.renren.modules.office.service.OfficeRepairService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;


/**
 * 维修记录
 *
 * @<NAME_EMAIL>
 * @since 1.0.0 2024-04-26
 */
@RestController
@RequestMapping("office/officerepair")
@Api(tags="维修记录")
public class OfficeRepairController {
    @Autowired
    private OfficeRepairService officeRepairService;

    @GetMapping("page")
    @ApiOperation("分页")
    @ApiImplicitParams({
        @ApiImplicitParam(name = Constant.PAGE, value = "当前页码，从1开始", paramType = "query", required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.LIMIT, value = "每页显示记录数", paramType = "query",required = true, dataType="int") ,
        @ApiImplicitParam(name = Constant.ORDER_FIELD, value = "排序字段", paramType = "query", dataType="String") ,
        @ApiImplicitParam(name = Constant.ORDER, value = "排序方式，可选值(asc、desc)", paramType = "query", dataType="String")
    })
    @RequiresPermissions("office:officerepair:page")
    public Result<PageData<OfficeRepairDTO>> page(@ApiIgnore @RequestParam Map<String, Object> params){
        PageData<OfficeRepairDTO> page = officeRepairService.page(params);

        return new Result<PageData<OfficeRepairDTO>>().ok(page);
    }

    @GetMapping("{id}")
    @ApiOperation("信息")
    @RequiresPermissions("office:officerepair:info")
    public Result<OfficeRepairDTO> get(@PathVariable("id") Long id){
        OfficeRepairDTO data = officeRepairService.get(id);

        return new Result<OfficeRepairDTO>().ok(data);
    }

    @PostMapping
    @ApiOperation("保存")
    @LogOperation("保存")
    @RequiresPermissions("office:officerepair:save")
    public Result save(@RequestBody OfficeRepairDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, AddGroup.class, DefaultGroup.class);

        officeRepairService.save(dto);

        return new Result();
    }

    @PutMapping
    @ApiOperation("修改")
    @LogOperation("修改")
    @RequiresPermissions("office:officerepair:update")
    public Result update(@RequestBody OfficeRepairDTO dto){
        //效验数据
        ValidatorUtils.validateEntity(dto, UpdateGroup.class, DefaultGroup.class);

        officeRepairService.update(dto);

        return new Result();
    }

    @DeleteMapping
    @ApiOperation("删除")
    @LogOperation("删除")
    @RequiresPermissions("office:officerepair:delete")
    public Result delete(@RequestBody Long[] ids){
        //效验数据
        AssertUtils.isArrayEmpty(ids, "id");

        officeRepairService.delete(ids);

        return new Result();
    }

    @GetMapping("export")
    @ApiOperation("导出")
    @LogOperation("导出")
    @RequiresPermissions("office:officerepair:export")
    public void export(@ApiIgnore @RequestParam Map<String, Object> params, HttpServletResponse response) throws Exception {
        List<OfficeRepairDTO> list = officeRepairService.list(params);

        ExcelUtils.exportExcelToTarget(response, null, "维修记录", list, OfficeRepairExcel.class);
    }

}