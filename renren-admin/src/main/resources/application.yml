# Tomcat
server:
  tomcat:
    uri-encoding: UTF-8
    threads:
      max: 1000
      min-spare: 30
  port: 8080
  servlet:
    context-path: /voice-changer
    session:
      cookie:
        http-only: true

knife4j:
  enable: true
  basic:
    enable: false
    username: admin
    password: admin
  setting:
    enableFooter: false

spring:
  # 环境 dev|test|prod
  profiles:
    active: prod
  messages:
    encoding: UTF-8
    basename: i18n/messages
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
  redis:
    database: 0
    host: ************
    port: 6379
    password: i9slhnbie0  # 密码（默认为空）
    timeout: 6000ms  # 连接超时时长（毫秒）
    lettuce:
      pool:
        max-active: 1000  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1ms      # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-idle: 10      # 连接池中的最大空闲连接
        min-idle: 5       # 连接池中的最小空闲连接

# 是否开启redis缓存  true开启   false关闭
renren:
  redis:
    open: true

voicechanger:
  redis:
    defaultReleaseTime: 60000
    defaultBlockTime: 10000

#mybatis
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: io.renren.modules.*.entity
  global-config:
    #数据库相关配置
    db-config:
      #主键类型
      id-type: ASSIGN_ID
    banner: false
  #原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  configuration-properties:
    prefix:
    blobType: BLOB
    boolValue: TRUE
