spring:
  datasource:
    druid:
      #MySQL
      driver-class-name: com.mysql.cj.jdbc.Driver
#      url: ***********************************************************************************************************************************************
      url: **********************************************************************************************************************************************
      username: voice_changer
      password: hH4BfHirYXYN3z6R
      #达梦8
#      driver-class-name: dm.jdbc.driver.DmDriver
#      url: jdbc:dm://*************:5236/renren_security?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&nullCatalogMeansCurrent=true
#      username: renren_security
#      password: 12345678
      #      #Oracle
      #      driver-class-name: oracle.jdbc.OracleDriver
      #      url: ***************************************
      #      username: renren_security
      #      password: 123456
      #      #SQLServer
      #      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      #      url: ************************************************************
      #      username: sa
      #      password: 123456
      #      #postgresql
      #      driver-class-name: org.postgresql.Driver
      #      url: *********************************************
      #      username: postgres
      #      password: 123456
      initial-size: 10
      max-active: 100
      min-idle: 10
      max-wait: 6000
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      #Oracle需要打开注释
      #      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        #login-username: admin
        #login-password: admin
        #达梦数据库，需要注释掉，其他数据库可以打开
#      filter:
#        stat:
#          log-slow-sql: true
#          slow-sql-millis: 1000
#          merge-sql: false
#        wall:
#          config:
#            multi-statement-allow: true

##多数据源的配置，需要引用renren-dynamic-datasource
#dynamic:
#  datasource:
#    slave1:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      url: *********************************************************
#      username: sa
#      password: 123456
#    slave2:
#      driver-class-name: org.postgresql.Driver
#      url: *********************************************
#      username: postgres
#      password: 123456

voicechanger:
  oldserver:
    yunmeng: http://************/renren-fast/generator/register/inheritAuthority
    dachun: http://**************/renren-fast/generator/register/inheritAuthority
    rzj: http://localhost:8090/renren-fast/generator/register/inheritAuthority
    tangdou: http://***************/renren-fast/generator/register/inheritAuthority
    shenshe: http://localhost:8090/renren-fast/generator/register/inheritAuthority
