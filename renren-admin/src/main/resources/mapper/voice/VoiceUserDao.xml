<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceUserDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceUserEntity" id="voiceUserMap">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="password" column="password"/>
        <result property="balance" column="balance"/>
        <result property="total" column="total"/>
        <result property="vipExpire" column="vip_expire"/>
        <result property="email" column="email"/>
        <result property="phone" column="phone"/>
        <result property="status" column="status"/>
        <result property="lastActiveTime" column="last_active_time"/>
        <result property="registerAddress" column="register_address"/>
        <result property="currentAddress" column="current_address"/>
        <result property="recommendBy" column="recommend_by"/>
        <result property="note" column="note"/>
        <result property="owner" column="owner"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>
