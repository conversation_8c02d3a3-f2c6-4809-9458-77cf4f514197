<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceUpdateDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceUpdateEntity" id="voiceUpdateMap">
        <result property="id" column="id"/>
        <result property="version" column="version"/>
        <result property="updateContent" column="update_content"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="downloadType" column="download_type"/>
        <result property="ready" column="ready"/>
        <result property="forceUpdate" column="force_update"/>
        <result property="owner" column="owner"/>
        <result property="createTime" column="create_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>
