<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceRecommendDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceRecommendEntity" id="voiceRecommendMap">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="downloadUrl" column="download_url"/>
        <result property="owner" column="owner"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>