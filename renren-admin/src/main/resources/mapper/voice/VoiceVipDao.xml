<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceVipDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceVipEntity" id="voiceVipMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="time" column="time"/>
        <result property="price" column="price"/>
        <result property="status" column="status"/>
        <result property="vipIndex" column="vip_index"/>
        <result property="owner" column="owner"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>
