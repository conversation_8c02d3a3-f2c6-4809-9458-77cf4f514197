<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceOrderDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceOrderEntity" id="voiceOrderMap">
        <result property="id" column="id"/>
        <result property="orderNumber" column="order_number"/>
        <result property="orderTitle" column="order_title"/>
        <result property="voiceUser" column="voice_user"/>
        <result property="amount" column="amount"/>
        <result property="notes" column="notes"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="settleId" column="settle_id"/>
        <result property="settleStatus" column="settle_status"/>
        <result property="owner" column="owner"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
        <result property="updateUser" column="update_user"/>
        <result property="updateTime" column="update_time"/>
        <result property="isDelete" column="is_delete"/>
    </resultMap>


</mapper>
