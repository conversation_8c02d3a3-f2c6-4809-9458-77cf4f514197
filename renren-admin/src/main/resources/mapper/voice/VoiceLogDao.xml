<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="io.renren.modules.voice.dao.VoiceLogDao">

    <resultMap type="io.renren.modules.voice.entity.VoiceLogEntity" id="voiceLogMap">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="logKey" column="log_key"/>
        <result property="logValue" column="log_value"/>
        <result property="message" column="message"/>
        <result property="level" column="level"/>
        <result property="owner" column="owner"/>
        <result property="createUser" column="create_user"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>