<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<groupId>io.renren</groupId>
		<artifactId>renren-security</artifactId>
		<version>5.3.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>renren-admin</artifactId>
	<packaging>jar</packaging>
	<description>renren-admin</description>

	<properties>
		<quartz.version>2.3.2</quartz.version>
		<shiro.version>1.12.0</shiro.version>
		<captcha.version>1.6.2</captcha.version>
		<easyexcel.version>3.2.1</easyexcel.version>
		<qiniu.version>7.2.27</qiniu.version>
		<aliyun.oss.version>2.8.3</aliyun.oss.version>
		<aliyun.core.version>3.2.2</aliyun.core.version>
		<qcloud.cos.version>5.4.4</qcloud.cos.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>io.renren</groupId>
			<artifactId>renren-common</artifactId>
			<version>5.3.0</version>
		</dependency>
		<dependency>
			<groupId>io.renren</groupId>
			<artifactId>renren-dynamic-datasource</artifactId>
			<version>5.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>${quartz.version}</version>
			<exclusions>
				<exclusion>
					<groupId>com.mchange</groupId>
					<artifactId>c3p0</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.zaxxer</groupId>
					<artifactId>HikariCP-java6</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-core</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
		    <groupId>org.apache.shiro</groupId>
		    <artifactId>shiro-spring</artifactId>
		    <version>${shiro.version}</version>
		</dependency>
		<dependency>
			<groupId>com.github.whvcse</groupId>
			<artifactId>easy-captcha</artifactId>
			<version>${captcha.version}</version>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>${easyexcel.version}</version>
		</dependency>
		<dependency>
			<groupId>com.qiniu</groupId>
			<artifactId>qiniu-java-sdk</artifactId>
			<version>${qiniu.version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>${aliyun.oss.version}</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>${aliyun.core.version}</version>
		</dependency>
		<dependency>
			<groupId>com.qcloud</groupId>
			<artifactId>cos_api</artifactId>
			<version>${qcloud.cos.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>slf4j-log4j12</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.macasaet.fernet</groupId>
			<artifactId>fernet-java8</artifactId>
			<version>1.5.0</version>
		</dependency>
	</dependencies>

	<build>
		<finalName>${project.artifactId}</finalName>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
